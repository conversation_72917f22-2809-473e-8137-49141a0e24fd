package services

import (
	"context"
	"log/slog"
	"os"
)

// LoggerService provides structured logging functionality
type LoggerService struct {
	logger *slog.Logger
}

// NewLoggerService creates a new logger service
func NewLoggerService(environment string) *LoggerService {
	var handler slog.Handler
	
	if environment == "production" {
		// JSON format for production
		handler = slog.NewJSONHandler(os.Stdout, &slog.HandlerOptions{
			Level: slog.LevelInfo,
		})
	} else {
		// Text format for development
		handler = slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
			Level: slog.LevelDebug,
		})
	}
	
	logger := slog.New(handler)
	
	return &LoggerService{
		logger: logger,
	}
}

// GetLogger returns the underlying slog.Logger
func (s *LoggerService) GetLogger() *slog.Logger {
	return s.logger
}

// LogError logs an error with context
func (s *LoggerService) LogError(ctx context.Context, msg string, err error, attrs ...slog.Attr) {
	args := []any{slog.String("error", err.Error())}
	for _, attr := range attrs {
		args = append(args, attr)
	}
	s.logger.ErrorContext(ctx, msg, args...)
}

// LogInfo logs an info message with context
func (s *LoggerService) LogInfo(ctx context.Context, msg string, attrs ...slog.Attr) {
	args := []any{}
	for _, attr := range attrs {
		args = append(args, attr)
	}
	s.logger.InfoContext(ctx, msg, args...)
}

// LogWarn logs a warning message with context
func (s *LoggerService) LogWarn(ctx context.Context, msg string, attrs ...slog.Attr) {
	args := []any{}
	for _, attr := range attrs {
		args = append(args, attr)
	}
	s.logger.WarnContext(ctx, msg, args...)
}

// LogDebug logs a debug message with context
func (s *LoggerService) LogDebug(ctx context.Context, msg string, attrs ...slog.Attr) {
	args := []any{}
	for _, attr := range attrs {
		args = append(args, attr)
	}
	s.logger.DebugContext(ctx, msg, args...)
}

// WithUserID returns a logger with user ID context
func (s *LoggerService) WithUserID(userID string) *slog.Logger {
	return s.logger.With(slog.String("user_id", userID))
}

// WithRequestID returns a logger with request ID context
func (s *LoggerService) WithRequestID(requestID string) *slog.Logger {
	return s.logger.With(slog.String("request_id", requestID))
}

// WithOperation returns a logger with operation context
func (s *LoggerService) WithOperation(operation string) *slog.Logger {
	return s.logger.With(slog.String("operation", operation))
}
