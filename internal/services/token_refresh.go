package services

import (
	"context"
	"fmt"
	"log/slog"
	"time"

	"golang.org/x/oauth2"
)

// TokenRefreshService handles OAuth token refresh operations
type TokenRefreshService struct {
	authService    *AuthService
	sessionService *SessionService
	logger         *LoggerService
}

// NewTokenRefreshService creates a new token refresh service
func NewTokenRefreshService(authService *AuthService, sessionService *SessionService, logger *LoggerService) *TokenRefreshService {
	return &TokenRefreshService{
		authService:    authService,
		sessionService: sessionService,
		logger:         logger,
	}
}

// RefreshTokenIfNeeded checks if a token needs refreshing and refreshes it if necessary
func (s *TokenRefreshService) RefreshTokenIfNeeded(ctx context.Context, token *oauth2.Token, sessionID string) (*oauth2.Token, error) {
	// Check if token is expired or will expire soon (within 5 minutes)
	if token.Valid() && token.Expiry.After(time.Now().Add(5*time.Minute)) {
		s.logger.LogDebug(ctx, "Token is still valid, no refresh needed",
			slog.String("session_id", sessionID),
			slog.Time("expiry", token.Expiry),
		)
		return token, nil
	}

	s.logger.LogInfo(ctx, "Token expired or expiring soon, attempting refresh",
		slog.String("session_id", sessionID),
		slog.Time("expiry", token.Expiry),
		slog.Bool("is_valid", token.Valid()),
	)

	// Check if we have a refresh token
	if token.RefreshToken == "" {
		err := fmt.Errorf("no refresh token available")
		s.logger.LogError(ctx, "Cannot refresh token: no refresh token available", err,
			slog.String("session_id", sessionID),
		)
		return nil, err
	}

	// Refresh the token
	tokenSource := s.authService.GetOAuthConfig().TokenSource(ctx, token)
	newToken, err := tokenSource.Token()
	if err != nil {
		s.logger.LogError(ctx, "Failed to refresh OAuth token", err,
			slog.String("session_id", sessionID),
		)
		return nil, fmt.Errorf("failed to refresh token: %w", err)
	}

	s.logger.LogInfo(ctx, "Successfully refreshed OAuth token",
		slog.String("session_id", sessionID),
		slog.Time("new_expiry", newToken.Expiry),
		slog.Bool("has_new_refresh_token", newToken.RefreshToken != ""),
	)

	// Update the session with the new token
	err = s.sessionService.UpdateSessionTokens(ctx, sessionID, newToken)
	if err != nil {
		s.logger.LogError(ctx, "Failed to update session with refreshed token", err,
			slog.String("session_id", sessionID),
		)
		// Return the new token anyway, but log the error
		return newToken, nil
	}

	s.logger.LogDebug(ctx, "Updated session with refreshed token",
		slog.String("session_id", sessionID),
	)

	return newToken, nil
}

// RefreshTokenForUser refreshes tokens for a specific user across all their sessions
func (s *TokenRefreshService) RefreshTokenForUser(ctx context.Context, userID string) error {
	s.logger.LogInfo(ctx, "Refreshing tokens for user",
		slog.String("user_id", userID),
	)

	// Get all sessions for the user
	sessionIDs, err := s.sessionService.GetUserSessions(ctx, userID)
	if err != nil {
		s.logger.LogError(ctx, "Failed to get user sessions for token refresh", err,
			slog.String("user_id", userID),
		)
		return fmt.Errorf("failed to get user sessions: %w", err)
	}

	if len(sessionIDs) == 0 {
		s.logger.LogWarn(ctx, "No active sessions found for user",
			slog.String("user_id", userID),
		)
		return fmt.Errorf("no active sessions found for user")
	}

	// Try to refresh tokens from the first valid session
	var lastErr error
	for _, sessionID := range sessionIDs {
		sessionData, err := s.sessionService.GetSession(ctx, sessionID)
		if err != nil {
			s.logger.LogWarn(ctx, "Failed to get session data, skipping",
				slog.String("session_id", sessionID),
				slog.String("error", err.Error()),
			)
			lastErr = err
			continue
		}

		token := &oauth2.Token{
			AccessToken:  sessionData.AccessToken,
			RefreshToken: sessionData.RefreshToken,
			Expiry:       sessionData.TokenExpiry,
		}

		refreshedToken, err := s.RefreshTokenIfNeeded(ctx, token, sessionID)
		if err != nil {
			s.logger.LogWarn(ctx, "Failed to refresh token for session, trying next",
				slog.String("session_id", sessionID),
				slog.String("error", err.Error()),
			)
			lastErr = err
			continue
		}

		// If we successfully refreshed, update all other sessions for this user
		if refreshedToken != token {
			s.updateAllUserSessions(ctx, userID, refreshedToken, sessionID)
		}

		s.logger.LogInfo(ctx, "Successfully refreshed tokens for user",
			slog.String("user_id", userID),
			slog.Int("total_sessions", len(sessionIDs)),
		)
		return nil
	}

	s.logger.LogError(ctx, "Failed to refresh tokens for any user session", lastErr,
		slog.String("user_id", userID),
		slog.Int("sessions_tried", len(sessionIDs)),
	)
	return fmt.Errorf("failed to refresh tokens for user: %w", lastErr)
}

// updateAllUserSessions updates all sessions for a user with the refreshed token
func (s *TokenRefreshService) updateAllUserSessions(ctx context.Context, userID string, token *oauth2.Token, excludeSessionID string) {
	sessionIDs, err := s.sessionService.GetUserSessions(ctx, userID)
	if err != nil {
		s.logger.LogError(ctx, "Failed to get user sessions for bulk update", err,
			slog.String("user_id", userID),
		)
		return
	}

	updatedCount := 0
	for _, sessionID := range sessionIDs {
		if sessionID == excludeSessionID {
			continue // Skip the session we already updated
		}

		err := s.sessionService.UpdateSessionTokens(ctx, sessionID, token)
		if err != nil {
			s.logger.LogWarn(ctx, "Failed to update session with refreshed token",
				slog.String("session_id", sessionID),
				slog.String("error", err.Error()),
			)
		} else {
			updatedCount++
		}
	}

	s.logger.LogInfo(ctx, "Updated user sessions with refreshed token",
		slog.String("user_id", userID),
		slog.Int("updated_sessions", updatedCount),
		slog.Int("total_sessions", len(sessionIDs)),
	)
}
