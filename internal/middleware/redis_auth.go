package middleware

import (
	"context"
	"encoding/json"
	"log/slog"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/sessions"
	"github.com/stefanoschrs/mealpal/internal/models"
	"github.com/stefanoschrs/mealpal/internal/services"
	"golang.org/x/oauth2"
)

// RedisAuthRequired middleware that uses Redis for session management
func RedisAuthRequired(store sessions.Store, sessionService *services.SessionService, userStore services.UserStoreInterface, tokenRefreshService *services.TokenRefreshService, logger *services.LoggerService) gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := c.Request.Context()
		requestID := GetRequestIDFromContext(c)

		// Create logger with request context
		contextLogger := logger.WithRequestID(requestID).WithOperation("auth_middleware")

		// Get session from cookie
		session, err := store.Get(c.Request, SessionName)
		if err != nil {
			contextLogger.ErrorContext(ctx, "Failed to get session from cookie",
				slog.String("error", err.Error()))
			c.Redirect(http.StatusFound, "/")
			c.Abort()
			return
		}

		// Get session data from cookie
		sessionDataJSON, ok := session.Values["session_data"].(string)
		if !ok || sessionDataJSON == "" {
			contextLogger.DebugContext(ctx, "No session data found in cookie")
			c.Redirect(http.StatusFound, "/")
			c.Abort()
			return
		}

		var sessionData models.SessionData
		if err := json.Unmarshal([]byte(sessionDataJSON), &sessionData); err != nil {
			contextLogger.ErrorContext(ctx, "Failed to unmarshal session data",
				slog.String("error", err.Error()))
			c.Redirect(http.StatusFound, "/")
			c.Abort()
			return
		}

		if !sessionData.IsLoggedIn || sessionData.SessionID == "" {
			contextLogger.DebugContext(ctx, "Invalid session data",
				slog.Bool("is_logged_in", sessionData.IsLoggedIn),
				slog.String("session_id", sessionData.SessionID))
			c.Redirect(http.StatusFound, "/")
			c.Abort()
			return
		}

		// Get full session data from Redis
		redisCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
		defer cancel()

		redisSessionData, err := sessionService.GetSession(redisCtx, sessionData.SessionID)
		if err != nil {
			contextLogger.ErrorContext(ctx, "Session not found in Redis",
				slog.String("error", err.Error()),
				slog.String("session_id", sessionData.SessionID))
			c.Redirect(http.StatusFound, "/")
			c.Abort()
			return
		}

		// Get user data from user store
		user, exists := userStore.GetUser(redisSessionData.UserID)
		if !exists {
			contextLogger.ErrorContext(ctx, "User not found in store",
				slog.String("user_id", redisSessionData.UserID),
				slog.String("session_id", sessionData.SessionID))
			c.Redirect(http.StatusFound, "/")
			c.Abort()
			return
		}

		// Add user context to logger
		userLogger := contextLogger.With(slog.String("user_id", user.ID))

		// Create OAuth token from session data
		oauthToken := &oauth2.Token{
			AccessToken:  redisSessionData.AccessToken,
			RefreshToken: redisSessionData.RefreshToken,
			Expiry:       redisSessionData.TokenExpiry,
		}

		// Check if token needs refreshing and refresh if necessary
		refreshedToken, err := tokenRefreshService.RefreshTokenIfNeeded(ctx, oauthToken, sessionData.SessionID)
		if err != nil {
			userLogger.ErrorContext(ctx, "Failed to refresh expired token",
				slog.String("error", err.Error()))
			c.Redirect(http.StatusFound, "/")
			c.Abort()
			return
		}

		userLogger.DebugContext(ctx, "Successfully authenticated user")

		// Store user and token in context for handlers to use
		c.Set("user", user)
		c.Set("oauth_token", refreshedToken)
		c.Set("session_id", sessionData.SessionID)
		c.Next()
	}
}

// GetSessionIDFromContext retrieves the session ID from the Gin context
func GetSessionIDFromContext(c *gin.Context) (string, bool) {
	sessionID, exists := c.Get("session_id")
	if !exists {
		return "", false
	}
	
	sessionIDStr, ok := sessionID.(string)
	return sessionIDStr, ok
}
