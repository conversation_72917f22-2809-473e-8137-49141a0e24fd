package middleware

import (
	"crypto/rand"
	"encoding/hex"

	"github.com/gin-gonic/gin"
)

const RequestIDKey = "request_id"

// RequestID middleware generates a unique request ID for each request
func RequestID() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Check if request ID is already set (e.g., from a load balancer)
		requestID := c.GetHeader("X-Request-ID")
		
		if requestID == "" {
			// Generate a new request ID
			requestID = generateRequestID()
		}
		
		// Set the request ID in the context
		c.Set(RequestIDKey, requestID)
		
		// Set the request ID in the response header
		c.<PERSON><PERSON>("X-Request-ID", requestID)
		
		c.Next()
	}
}

// GetRequestIDFromContext retrieves the request ID from the Gin context
func GetRequestIDFromContext(c *gin.Context) string {
	if requestID, exists := c.Get(RequestIDKey); exists {
		if id, ok := requestID.(string); ok {
			return id
		}
	}
	return ""
}

// generateRequestID generates a random request ID
func generateRequestID() string {
	bytes := make([]byte, 8)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}
