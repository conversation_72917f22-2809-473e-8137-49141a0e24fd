package handlers

import (
	"context"
	"crypto/rand"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"log/slog"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/sessions"
	"github.com/stefanoschrs/mealpal/internal/middleware"
	"github.com/stefanoschrs/mealpal/internal/models"
	"github.com/stefanoschrs/mealpal/internal/services"
	"golang.org/x/oauth2"
)

type RedisAuthHandler struct {
	authService    *services.AuthService
	store          sessions.Store
	foodLogService *services.FoodLogService
	sessionService *services.SessionService
	logger         *services.LoggerService
}

func NewRedisAuthHandler(authService *services.AuthService, store sessions.Store, foodLogService *services.FoodLogService, sessionService *services.SessionService, logger *services.LoggerService) *RedisAuthHandler {
	return &RedisAuthHandler{
		authService:    authService,
		store:          store,
		foodLogService: foodLogService,
		sessionService: sessionService,
		logger:         logger,
	}
}

func (h *RedisAuthHandler) Login(c *gin.Context) {
	// Generate a random state token
	state := generateRedisStateToken()
	
	// Store state in session for verification
	session, _ := h.store.Get(c.Request, middleware.SessionName)
	session.Values["oauth_state"] = state
	session.Save(c.Request, c.Writer)

	// Redirect to Google OAuth
	authURL := h.authService.GetAuthURL(state)
	c.Redirect(http.StatusFound, authURL)
}

func (h *RedisAuthHandler) Callback(c *gin.Context) {
	ctx := c.Request.Context()
	requestID := middleware.GetRequestIDFromContext(c)

	// Create logger with request context
	contextLogger := h.logger.WithRequestID(requestID).WithOperation("oauth_callback")

	contextLogger.InfoContext(ctx, "Processing OAuth callback")

	// Verify state token
	session, err := h.store.Get(c.Request, middleware.SessionName)
	if err != nil {
		contextLogger.ErrorContext(ctx, "Failed to get session during OAuth callback",
			slog.String("error", err.Error()))
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"error": "Session error",
		})
		return
	}

	storedState, ok := session.Values["oauth_state"].(string)
	receivedState := c.Query("state")
	if !ok || storedState != receivedState {
		contextLogger.WarnContext(ctx, "Invalid state token in OAuth callback",
			slog.Bool("state_exists", ok),
			slog.String("received_state", receivedState))
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"error": "Invalid state token",
		})
		return
	}

	// Exchange code for token
	code := c.Query("code")
	if code == "" {
		contextLogger.WarnContext(ctx, "No authorization code received in OAuth callback")
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"error": "No authorization code received",
		})
		return
	}

	contextLogger.DebugContext(ctx, "Exchanging authorization code for token")

	var token *oauth2.Token
	token, err = h.authService.ExchangeCode(ctx, code)
	if err != nil {
		contextLogger.ErrorContext(ctx, "Failed to exchange authorization code",
			slog.String("error", err.Error()))
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": "Failed to exchange authorization code",
		})
		return
	}

	contextLogger.DebugContext(ctx, "Successfully exchanged code for token",
		slog.Time("token_expiry", token.Expiry),
		slog.Bool("has_refresh_token", token.RefreshToken != ""))

	// Get user info from Google
	user, err := h.authService.GetUserInfo(ctx, token)
	if err != nil {
		contextLogger.ErrorContext(ctx, "Failed to get user information from Google",
			slog.String("error", err.Error()))
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": "Failed to get user information",
		})
		return
	}

	// Add user context to logger
	userLogger := contextLogger.With(slog.String("user_id", user.ID), slog.String("user_email", user.Email))

	userLogger.InfoContext(ctx, "User authenticated successfully")

	// Check if this is an existing user and merge stored data
	finalUser := h.mergeUserData(user)

	// For new users, create spreadsheet immediately
	if finalUser.SpreadsheetID == "" {
		userLogger.InfoContext(ctx, "Creating spreadsheet for new user")
		err = h.createSpreadsheetForUser(ctx, finalUser, token)
		if err != nil {
			// Log error but don't fail login - spreadsheet can be created later
			userLogger.WarnContext(ctx, "Failed to create spreadsheet for new user",
				slog.String("error", err.Error()))
		} else {
			userLogger.InfoContext(ctx, "Successfully created spreadsheet for new user")
		}
	}

	// Create Redis session
	sessionCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	userLogger.DebugContext(ctx, "Creating Redis session")
	sessionID, err := h.sessionService.CreateSession(sessionCtx, finalUser.ID, token)
	if err != nil {
		userLogger.ErrorContext(ctx, "Failed to create Redis session",
			slog.String("error", err.Error()))
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": "Failed to create session",
		})
		return
	}

	userLogger.InfoContext(ctx, "Successfully created Redis session",
		slog.String("session_id", sessionID))

	// Store minimal session data in cookie
	sessionData := models.SessionData{
		SessionID:  sessionID,
		IsLoggedIn: true,
	}

	sessionDataJSON, err := json.Marshal(sessionData)
	if err != nil {
		userLogger.ErrorContext(ctx, "Failed to marshal session data",
			slog.String("error", err.Error()))
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": "Failed to save session",
		})
		return
	}

	session.Values["session_data"] = string(sessionDataJSON)
	err = session.Save(c.Request, c.Writer)
	if err != nil {
		userLogger.ErrorContext(ctx, "Failed to save session cookie",
			slog.String("error", err.Error()))
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": "Failed to save session",
		})
		return
	}

	userLogger.InfoContext(ctx, "OAuth login completed successfully")

	// Redirect to dashboard
	c.Redirect(http.StatusFound, "/dashboard")
}

func (h *RedisAuthHandler) Logout(c *gin.Context) {
	session, _ := h.store.Get(c.Request, middleware.SessionName)
	
	// Get session data to clean up Redis session
	sessionDataJSON, ok := session.Values["session_data"].(string)
	if ok && sessionDataJSON != "" {
		var sessionData models.SessionData
		if err := json.Unmarshal([]byte(sessionDataJSON), &sessionData); err == nil {
			// Delete Redis session
			ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
			defer cancel()
			h.sessionService.DeleteSession(ctx, sessionData.SessionID)
		}
	}
	
	// Clear cookie session
	session.Values = make(map[interface{}]interface{})
	session.Options.MaxAge = -1
	session.Save(c.Request, c.Writer)

	c.Redirect(http.StatusFound, "/")
}

// mergeUserData merges fresh Google user data with existing stored user data
func (h *RedisAuthHandler) mergeUserData(googleUser *models.User) *models.User {
	// Check if user exists in store
	storedUser, exists := h.foodLogService.GetUserStore().GetUser(googleUser.ID)
	if exists {
		// Merge: use stored data but update with fresh Google info
		storedUser.Email = googleUser.Email
		storedUser.Name = googleUser.Name
		storedUser.Picture = googleUser.Picture
		// Don't store tokens in user store anymore - they go to Redis
		// Keep existing SpreadsheetID

		// Save updated user data (without tokens)
		h.foodLogService.GetUserStore().SaveUser(storedUser)
		h.logger.LogDebug(context.Background(), "Existing user logged in",
			slog.String("user_id", storedUser.ID),
			slog.String("email", storedUser.Email),
			slog.String("spreadsheet_id", storedUser.SpreadsheetID))
		return storedUser
	}

	// New user - save to store (without tokens)
	googleUser.AccessToken = ""
	googleUser.RefreshToken = ""
	h.foodLogService.GetUserStore().SaveUser(googleUser)
	h.logger.LogInfo(context.Background(), "New user signed up",
		slog.String("user_id", googleUser.ID),
		slog.String("email", googleUser.Email))
	return googleUser
}

// createSpreadsheetForUser creates a spreadsheet for a new user
func (h *RedisAuthHandler) createSpreadsheetForUser(ctx context.Context, user *models.User, token *oauth2.Token) error {
	// Use the sheets service to create an empty spreadsheet
	sheetsService := services.NewSheetsService(h.authService.GetOAuthConfig(), h.foodLogService.GetUserStore())

	err := sheetsService.CreateSpreadsheetForUser(ctx, user, token)
	if err != nil {
		return fmt.Errorf("failed to create initial spreadsheet: %w", err)
	}

	h.logger.LogInfo(context.Background(), "Created spreadsheet for new user",
		slog.String("user_id", user.ID),
		slog.String("email", user.Email))
	return nil
}

func generateRedisStateToken() string {
	b := make([]byte, 32)
	rand.Read(b)
	return base64.URLEncoding.EncodeToString(b)
}
